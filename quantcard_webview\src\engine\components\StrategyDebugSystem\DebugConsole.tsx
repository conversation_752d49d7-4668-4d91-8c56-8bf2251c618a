/**
 * 🎯 策略调试控制台主组件
 * 基于旧版前端StrategyDebugConsole重构，赛博朋克风格
 */

import React, { useEffect, useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import SignalTable from './SignalTable';
import TimingSignalTable from './TimingSignalTable';
import type { DebugConsoleProps, Signal, Stock, TimingSignal } from './types';
import './DebugConsole.scss';

// 日志显示组件
const LogDisplay: React.FC<{ logs: string[] }> = ({ logs = [] }) => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [currentLogIndex, setCurrentLogIndex] = React.useState(0);

  React.useEffect(() => {
    if (logs && logs.length > 0) {
      setCurrentLogIndex(logs.length - 1); // 总是显示最新的日志
    }
  }, [logs]);

  if (!logs || logs.length === 0) {
    return (
      <div className="empty-logs">
        <span style={{ color: '#f8f8f2' }}>暂无执行日志</span>
      </div>
    );
  }

  return (
    <div className={`logs-wrapper ${isExpanded ? 'expanded' : ''}`}>
      <div className="logs-header">
        <div className="logs-title">
          <span style={{ color: '#bd93f9', fontWeight: 600 }}>执行日志</span>
          <span className="realtime-tag">实时</span>
        </div>
        <div className="logs-content">
          <div className="log-content">
            <span className="message">{logs[currentLogIndex]}</span>
          </div>
        </div>
        <button
          className="expand-button"
          onClick={() => setIsExpanded(!isExpanded)}
          title={isExpanded ? "收起" : "展开全部日志"}
        >
          {isExpanded ? '▲' : '▼'}
        </button>
      </div>
      {isExpanded && (
        <div className="logs-detail">
          {logs.map((log, index) => (
            <div key={index} className="log-content">
              <span className="message">{log}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const DebugConsole: React.FC<DebugConsoleProps> = ({
  result,
  onClose,
  strategyType,
  loading = false
}) => {
  const logsEndRef = useRef<HTMLDivElement>(null);

  // 确定当前的策略类型
  const currentStrategyType = useMemo(() => {
    // 优先使用传入的策略类型
    if (strategyType) return strategyType;

    // 其次使用返回数据中的策略类型
    if (result?.data?.strategy_type) return result.data.strategy_type;

    // 最后尝试根据信号数据结构判断
    if (result?.data?.signals && result.data.signals.length > 0) {
      const signal = result.data.signals[0];
      // 如果有direction字段，则认为是择时策略
      if ('direction' in signal) return 'timing';
    }

    // 默认为选股策略
    return 'filter';
  }, [strategyType, result?.data]);

  // 计算表格的理想高度
  const tableHeight = useMemo(() => {
    if (!result?.data?.signals) return 0;
    const rowHeight = 36;
    const headerHeight = 40;
    const paginationHeight = 50;
    const padding = 32;

    // 计算实际需要的行数（最少显示5行，最多显示10行）
    const rowCount = Math.min(10, Math.max(5, result.data.signals.length));
    return rowCount * rowHeight + headerHeight + paginationHeight + padding;
  }, [result?.data?.signals]);

  // 计算控制台的总高度
  const consoleHeight = useMemo(() => {
    const headerHeight = 52;
    const logsHeight = 40;
    const resultsTitleHeight = 40;
    const marginBottom = 16;

    return headerHeight + logsHeight + resultsTitleHeight + tableHeight + marginBottom;
  }, [tableHeight]);

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && onClose) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [onClose]);

  useEffect(() => {
    // 自动滚动到最新日志
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [result?.logs]);

  // 转换信号数据格式以兼容旧版组件
  const convertSignalsForOldComponents = (signals: Signal[]): Stock[] | TimingSignal[] => {
    if (currentStrategyType === 'timing') {
      return signals.map(signal => ({
        symbol: signal.symbol,
        name: signal.name,
        direction: signal.direction as 'BUY' | 'SELL' | 'UNKNOWN',
        trigger_condition: signal.trigger_condition,
        latest_price: signal.price,
        ma_diff_pct: signal.change_pct,
        market_data: {
          name: signal.name,
          price: signal.price,
          market_cap: signal.market_cap,
          day60_change: signal.change_pct,
          industry: signal.metadata?.industry || '-'
        }
      }));
    } else {
      return signals.map(signal => ({
        code: signal.symbol,
        symbol: signal.symbol,
        name: signal.name,
        price: signal.price,
        market_data: {
          name: signal.name,
          price: signal.price,
          market_cap: signal.market_cap,
          day60_change: signal.change_pct,
          industry: signal.metadata?.industry || '-'
        }
      }));
    }
  };

  const renderLogs = () => {
    const logs = result?.logs || [];
    return <LogDisplay logs={logs} />;
  };

  const renderSignalTable = () => {
    if (!result?.data?.signals || result.data.signals.length === 0) return null;

    // 开发环境下记录信号数据格式进行调试
    if (process.env.NODE_ENV !== 'production') {
      console.log('信号数据类型: ', currentStrategyType);
      console.log('第一个信号示例:', result.data.signals[0]);
    }

    const convertedSignals = convertSignalsForOldComponents(result.data.signals);

    // 根据策略类型选择不同的表格组件
    if (currentStrategyType === 'timing') {
      return <TimingSignalTable signals={convertedSignals as TimingSignal[]} />;
    } else {
      return <SignalTable signals={convertedSignals as Stock[]} />;
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="debug-content">
          <div className="debug-logs">
            <LogDisplay logs={['策略执行中...']} />
          </div>
          <div className="debug-results">
            <div className="loading-placeholder">正在执行策略...</div>
          </div>
        </div>
      );
    }

    if (!result) return null;

    // 检查是否有信号数据
    const hasSignals = result.data && Array.isArray(result.data.signals) && result.data.signals.length > 0;

    if (!hasSignals) {
      // 如果成功但没有信号数据
      if (result.success) {
        return (
          <div className="debug-info">
            <span>执行成功，但未返回{currentStrategyType === 'timing' ? '交易' : '股票'}信号</span>
            {renderLogs()}
          </div>
        );
      }
      // 执行失败
      return (
        <div className="debug-error">
          <span>执行出错：{result.message || '未获取到有效数据'}</span>
          {renderLogs()}
        </div>
      );
    }

    return (
      <div className="debug-content">
        {/* 日志面板 */}
        <div className="debug-logs">
          {renderLogs()}
        </div>

        {/* 结果面板 - 根据策略类型使用不同的信号表格 */}
        <div className="debug-results">
          {renderSignalTable()}
        </div>
      </div>
    );
  };

  if (!result && !loading) return null;

  return (
    <div className="debug-console-overlay" onClick={onClose}>
      <motion.div
        className="strategy-debug-console"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        style={{
          maxHeight: `min(${consoleHeight}px, calc(100vh - 32px))`,
          height: consoleHeight
        }}
        onClick={(e) => e.stopPropagation()}
      >
      <div className="debug-header">
        <div className="header-left">
          <div className="header-main">
            <div className={`status-tag ${result?.success ? 'success' : 'error'}`}>
              <span className="status-icon">{loading ? '🔄' : result?.success ? '✓' : '❌'}</span>
              <span>{loading ? '执行中' : result?.success ? '执行成功' : '执行失败'}</span>
            </div>
            <span style={{ marginLeft: 8, color: '#f8f8f2' }}>
              {loading ? '策略正在执行...' : result?.message || (result?.success ? '策略执行成功' : '策略执行失败')}
            </span>
          </div>
          {result?.data && (
            <div className="header-sub">
              <span style={{ color: '#6272a4' }}>
                {result.data.filter_condition} | 执行时间: {result.data.execution_time}
              </span>
            </div>
          )}
        </div>
        <div className="header-right">
          <button onClick={onClose} className="close-button">✕</button>
        </div>
      </div>
      {renderContent()}
      <div ref={logsEndRef} />
      </motion.div>
    </div>
  );
};

export default React.memo(DebugConsole);

export default DebugConsole; 