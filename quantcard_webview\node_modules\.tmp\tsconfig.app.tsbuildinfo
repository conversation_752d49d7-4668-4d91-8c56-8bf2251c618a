{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/assets/index.ts", "../../src/assets/audio/audiomanager.ts", "../../src/engine/gameshell.tsx", "../../src/engine/scenerouter.tsx", "../../src/engine/components/gamenotification.tsx", "../../src/engine/components/savestrategypanel.tsx", "../../src/engine/components/index.ts", "../../src/engine/components/advancedparametereditor/conditionalparameters.tsx", "../../src/engine/components/advancedparametereditor/inlineparametereditor.tsx", "../../src/engine/components/advancedparametereditor/parameterfield.tsx", "../../src/engine/components/advancedparametereditor/parametergroup.tsx", "../../src/engine/components/advancedparametereditor/parametermodal.tsx", "../../src/engine/components/advancedparametereditor/index.ts", "../../src/engine/components/advancedparametereditor/types.ts", "../../src/engine/components/cyberbutton/cyberbutton.tsx", "../../src/engine/components/cyberbutton/index.ts", "../../src/engine/components/interactiveworldmap/interactiveworldmap.tsx", "../../src/engine/components/interactiveworldmap/index.ts", "../../src/engine/components/inventorydrawer/inventorydrawer.tsx", "../../src/engine/components/inventorydrawer/index.ts", "../../src/engine/components/performancechart3d/performancechart3d.tsx", "../../src/engine/components/performancechart3d/index.ts", "../../src/engine/components/smartparameteroptimizer/smartparameteroptimizer.tsx", "../../src/engine/components/smartparameteroptimizer/index.ts", "../../src/engine/components/strategydebugsystem/debugconsole.tsx", "../../src/engine/components/strategydebugsystem/logdisplay.tsx", "../../src/engine/components/strategydebugsystem/signaltable.tsx", "../../src/engine/components/strategydebugsystem/index.ts", "../../src/engine/components/strategydebugsystem/types.ts", "../../src/engine/components/strategytypeselector/grouptypecard.tsx", "../../src/engine/components/strategytypeselector/strategytypemodal.tsx", "../../src/engine/components/strategytypeselector/strategytypeselector.tsx", "../../src/engine/components/strategytypeselector/timingsymbolsinput.tsx", "../../src/engine/components/strategytypeselector/index.ts", "../../src/engine/components/strategytypeselector/types.ts", "../../src/engine/components/universalcard/universalcard.tsx", "../../src/engine/components/universalcard/index.ts", "../../src/engine/components/auth/loginmodal.tsx", "../../src/engine/components/codexpanel/codexpanel.tsx", "../../src/engine/components/codexpanel/index.ts", "../../src/engine/components/ui/nav/unifiedmobilenav.tsx", "../../src/engine/data/strategytemplates.ts", "../../src/engine/scenes/adventurescene.tsx", "../../src/engine/scenes/arenascene.tsx", "../../src/engine/scenes/backtestscene.tsx", "../../src/engine/scenes/battlescene.tsx", "../../src/engine/scenes/carddrawscene.tsx", "../../src/engine/scenes/profilescene.tsx", "../../src/engine/scenes/settingsscene.tsx", "../../src/engine/scenes/shopscene.tsx", "../../src/engine/scenes/strategycreationscene.tsx", "../../src/engine/scenes/strategyoptimizationscene.tsx", "../../src/engine/scenes/testscene.tsx", "../../src/engine/scenes/worldmapscene.tsx", "../../src/services/websocketprovider.tsx", "../../src/services/unifiedwebsocket.ts", "../../src/services/api/strategy.ts", "../../src/store/index.ts", "../../src/store/rootstore.ts", "../../src/store/hooks/index.ts", "../../src/store/hooks/useauth.ts", "../../src/store/hooks/usecardsstate.ts", "../../src/store/hooks/usegamestate.ts", "../../src/store/hooks/usestrategystate.ts", "../../src/store/hooks/useuistate.ts", "../../src/store/slices/authslice.ts", "../../src/store/slices/cardsslice.ts", "../../src/store/slices/gameslice.ts", "../../src/store/slices/strategyslice.ts", "../../src/store/slices/uislice.ts", "../../src/store/types/store.ts", "../../src/styles/styled.d.ts", "../../src/styles/themes/themeprovider.tsx", "../../src/styles/themes/dual-theme.ts", "../../src/types/game.ts", "../../src/types/index.ts", "../../src/utils/componentpool.tsx", "../../src/utils/assetmanager.ts", "../../src/utils/templatesentence.ts"], "errors": true, "version": "5.8.3"}