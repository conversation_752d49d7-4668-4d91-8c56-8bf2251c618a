/**
 * 🎯 策略调试系统类型定义
 * 基于旧前端StrategyDebugConsole的类型扩展
 */

export interface PriceAnalysis {
  average: number;
  highest: number;
  lowest: number;
  deviation: number;
}

export interface Statistics {
  total: number;
  matched: number;
  ratio: string;
  price_analysis?: PriceAnalysis;
}

// 统一的信号接口，匹配后端 Signal 类
export interface Signal {
  // 基础信号字段
  symbol: string;
  name: string;
  direction: string;
  confidence: number;
  type: string;
  trigger_condition: string;

  // 市场数据字段
  price: number;
  change_pct: number;
  volume: number;
  market_cap?: number;

  // 择时信号专用字段
  signal_strength?: number;

  // 内部使用字段
  id: string;
  strategy_id: string;
  group_id: string;
  timestamp: string;

  // 元数据字段
  metadata: Record<string, any>;

  // 计算属性
  reason: string;
}

// 为了向后兼容，保留原有接口作为类型别名
export type FilterSignal = Signal;
export type TimingSignal = Signal;

export interface DebugResult {
  success: boolean;
  message: string;
  logs?: string[];
  data: {
    signals: Signal[];
    statistics: Statistics;
    filter_condition: string;
    execution_time: string;
    strategy_type?: 'filter' | 'timing';
    logs?: string[];
  } | null;
}

export interface DebugConsoleProps {
  result: DebugResult | null;
  onClose?: () => void;
  strategyType?: 'filter' | 'timing';
  loading?: boolean;
}

export interface LogDisplayProps {
  logs: string[];
  expanded?: boolean;
  onToggleExpanded?: () => void;
}

export interface SignalTableProps {
  signals: Signal[];
  strategyType: 'filter' | 'timing';
  loading?: boolean;
}