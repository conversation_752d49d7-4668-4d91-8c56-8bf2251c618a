/**
 * 🎯 策略创建工坊 - Strategy Creation Workshop
 * 重新设计的策略创建界面，集成可折叠库存区域、策略组配置、参数编辑等功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useCardsState } from '../../store/hooks/useCardsState';
import { useStrategyState } from '../../store/hooks/useStrategyState';
import { UniversalCard } from '../components/UniversalCard';
import type { UnifiedCardData } from '../components/UniversalCard';
import type { StrategyCardInstance, StrategyGroup } from '../../types/game';
// 参数编辑器已迁移至 AdvancedParameterEditor 模块
import SaveStrategyPanel from '../components/SaveStrategyPanel'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'
import { templateService } from '../../services/templateService'
import { useAuth } from '../../store/hooks/useAuth'
import { StrategyTypeModal } from '../components/StrategyTypeSelector';
import AdvancedParameterModal from '../components/AdvancedParameterEditor/ParameterModal';
import { DebugConsole } from '../components/StrategyDebugSystem';
import type { StrategyTypeConfig } from '../components/StrategyTypeSelector/types';
import { debugStrategy, fetchStrategyTemplate, createStrategyGroup } from '../../services/api/strategy';
import { buildCoreSentence } from '../../utils/templateSentence';
import type { DebugResult } from '../components/StrategyDebugSystem/types';
import type { StrategyParameter } from '../components/AdvancedParameterEditor/types';

// 🎨 主容器内容区域
const CreationContent = styled.div`
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 25%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
`;

// 🎮 工具栏区域
const ToolBarArea = styled.div`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
`;

const ToolBarLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex: 1;
    min-width: 200px;
  }
`;

const ToolBarRight = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
`;



const ActionButton = styled(motion.button)<{ $variant?: 'primary' | 'secondary' | 'success' | 'danger' }>`
  padding: 0.5rem 1rem;
  border: 2px solid ${props => {
    switch(props.$variant) {
      case 'primary': return '#3b82f6'
      case 'success': return '#10b981'
      case 'danger': return '#ef4444'
      default: return '#64748b'
    }
  }};
  background: ${props => {
    switch(props.$variant) {
      case 'primary': return '#3b82f6'
      case 'success': return '#10b981'
      case 'danger': return '#ef4444'
      default: return 'transparent'
    }
  }};
  color: ${props => {
    switch(props.$variant) {
      case 'primary':
      case 'success':
      case 'danger': return 'white'
      default: return '#64748b'
    }
  }};
  border-radius: 8px;
  font-family: "Inter", sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px ${props => {
      switch(props.$variant) {
        case 'primary': return 'rgba(59, 130, 246, 0.3)'
        case 'success': return 'rgba(16, 185, 129, 0.3)'
        case 'danger': return 'rgba(239, 68, 68, 0.3)'
        default: return 'rgba(100, 116, 139, 0.2)'
      }
    }};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  @media (max-width: 640px) {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
`;

const ModeSelector = styled.div`
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 2px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  
  @media (max-width: 640px) {
    order: -1;
    width: 100%;
  }
`;

const ModeOption = styled(motion.button)<{ $active: boolean }>`
  padding: 0.5rem 1rem;
  border: none;
  background: ${props => props.$active ? '#3b82f6' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#64748b'};
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.$active ? '#2563eb' : 'rgba(59, 130, 246, 0.1)'};
  }
`;

// 📋 主工作区 - 新布局
const WorkArea = styled.div`
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
`;

// 📦 库存区域 - 可折叠的50%屏幕区域（折叠时仅显示标签）
const InventoryArea = styled(motion.div)<{ $isExpanded: boolean }>`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: ${props => props.$isExpanded ? '50vh' : '0px'};
  background: ${props => props.$isExpanded ? 'linear-gradient(135deg, #3b2f8b 0%, #6b46c1 25%, #7c3aed 50%, #8b5cf6 75%, #a855f7 100%)' : 'transparent'};
  border-radius: ${props => props.$isExpanded ? '20px 20px 0 0' : '0'};
  box-shadow: ${props => props.$isExpanded ? '0 -4px 32px rgba(139, 92, 246, 0.3)' : 'none'};
  z-index: 200;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
`;

const InventoryToggle = styled(motion.button)`
  position: absolute;
  top: -40px;
  left: 16px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 20px 20px 0 0;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 -2px 8px rgba(139, 92, 246, 0.3);
  pointer-events: auto;
  
  &:hover {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
    transform: translateY(-2px);
  }
`;

const InventoryHeader = styled.div<{ $isExpanded: boolean }>`
  padding: ${props => props.$isExpanded ? '1.5rem 1.5rem 1rem' : '0'};
  background: ${props => props.$isExpanded ? 'rgba(255, 255, 255, 0.1)' : 'transparent'};
  backdrop-filter: ${props => props.$isExpanded ? 'blur(10px)' : 'none'};
  border-bottom: ${props => props.$isExpanded ? '1px solid rgba(255, 255, 255, 0.2)' : 'none'};
  display: ${props => props.$isExpanded ? 'flex' : 'none'};
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
`;

const InventoryTitle = styled.h3<{ $isExpanded: boolean }>`
  color: #ffffff;
  font-size: ${props => props.$isExpanded ? '1.25rem' : '1rem'};
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &::before {
    content: '🎒';
    font-size: ${props => props.$isExpanded ? '1.5rem' : '1.25rem'};
  }
`;



const StatBadge = styled.div<{ $color: string }>`
  background: ${props => props.$color}20;
  border: 1px solid ${props => props.$color};
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const InventoryContent = styled.div<{ $isExpanded: boolean }>`
  flex: 1;
  overflow-y: auto;
  padding: ${props => props.$isExpanded ? '1rem 1.5rem' : '0'};
  opacity: ${props => props.$isExpanded ? 1 : 0};
  pointer-events: ${props => props.$isExpanded ? 'auto' : 'none'};
  transition: opacity 0.3s ease;
  display: ${props => props.$isExpanded ? 'block' : 'none'};
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const InventoryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 6px;
  justify-content: center;
`;

// 🃏 库存卡片包装器
const InventoryCardWrapper = styled(motion.div)<{ $available: boolean }>`
  position: relative;
  opacity: ${props => props.$available ? 1 : 0.6};
  cursor: ${props => props.$available ? 'grab' : 'not-allowed'};
  
  &:active {
    cursor: ${props => props.$available ? 'grabbing' : 'not-allowed'};
  }
  
  ${props => !props.$available && `
    &::after {
      content: '已用完';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(239, 68, 68, 0.9);
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-size: 0.7rem;
      font-weight: 600;
      z-index: 10;
    }
  `}
`;

const CardAnchor = styled.div`
  position: relative;
  display: inline-block;
`;

const QuantityBadge = styled.div<{ $count: number }>`
  position: absolute;
  top: 2px;
  right: 2px;
  background: ${props => props.$count > 0 ? '#10b981' : '#ef4444'};
  color: white;
  border-radius: 10px;
  height: 20px;
  padding: 0 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.65rem;
  font-weight: 700;
  border: 2px solid #ffffff;
  z-index: 15;
  line-height: 1;
`;

// 🎯 策略配置区域 - 主工作区
const ConfigurationArea = styled.div`
  flex: 1;
  background: rgba(248, 250, 252, 0.8);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 60px; /* 为折叠标签留空间 */
`;

const ConfigGrid = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
  background-size: 24px 24px;
  opacity: 0.6;
`;

const ConfigContent = styled.div`
  position: relative;
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
`;

const GroupHeader = styled.div`
  display: none;
`;

const GroupEditableTitle = styled.input`
  background: transparent;
  border: 1px solid transparent;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-family: inherit;
  min-width: 200px;
  
  &:hover {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(59, 130, 246, 0.05);
  }
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

const GroupEditableDescription = styled.textarea`
  background: transparent;
  border: 1px solid transparent;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 400;
  margin: 0.5rem 0 0 0;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-family: inherit;
  resize: none;
  min-height: 40px;
  width: 100%;
  
  &:hover {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(59, 130, 246, 0.05);
  }
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

const GroupActions = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`;

// 🎴 策略卡片区域
const CardsArea = styled.div`
  min-height: clamp(260px, 40vh, 520px);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.02);
  position: relative;
  transition: all 0.3s ease;
  
  &.drag-over {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.08);
    border-style: solid;
  }
`;

const CardsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(115px, 1fr));
  gap: 6px;
  min-height: 120px;
`;

const EmptyCardsHint = styled(motion.div)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #64748b;
  pointer-events: none;
  
  .icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
  }
  
  .title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
  }
`;

const ConfigCardWrapper = styled(motion.div)<{ $isSelected?: boolean }>`
  position: relative;
  cursor: pointer;
  
  ${props => props.$isSelected && `
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid #3b82f6;
      border-radius: 20px;
      background: rgba(59, 130, 246, 0.1);
      z-index: -1;
    }
  `}
`;

const CardRemoveButton = styled(motion.button)`
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ef4444;
  border: 2px solid white;
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: bold;
  z-index: 20;
  line-height: 1;
  
  &:hover {
    background: #dc2626;
    transform: scale(1.05);
  }
`;

// 🎯 策略创建主组件（简化结构）
function StrategyCreationScene({ sceneData }: { sceneData?: any }) {
  const { inventory, loading: inventoryLoading, loadInventory, getAvailableQuantity, consumeCard, addCards } = useCardsState();
  const { groups, createGroup, addCardToGroup, currentGroupId, setCurrentGroup } = useStrategyState();
  const { token } = useAuth();
  
  // 🎮 组件状态
  const [isInventoryExpanded, setIsInventoryExpanded] = useState(true);
  const [currentGroup, setCurrentGroupState] = useState<StrategyGroup | null>(null);
  const [executionMode, setExecutionMode] = useState<'sequential' | 'parallel'>('sequential');
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [editingCard, setEditingCard] = useState<StrategyCardInstance | null>(null);
const [editingParameters, setEditingParameters] = useState<Record<string, any>>({});
const [editingParamDefs, setEditingParamDefs] = useState<Record<string, StrategyParameter>>({});
const [editingParamGroups, setEditingParamGroups] = useState<Record<string, any>>({});
const [paramLoading, setParamLoading] = useState(false);
const [paramError, setParamError] = useState<string | undefined>(undefined);

  const [isDragOver, setIsDragOver] = useState(false);
  const [draggedCard, setDraggedCard] = useState<UnifiedCardData | null>(null);
  const [isSavePanelOpen, setIsSavePanelOpen] = useState(false);
  
  // 🎯 新增的状态管理
  const [showTypeModal, setShowTypeModal] = useState(true);
  const [strategyTypeConfig, setStrategyTypeConfig] = useState<StrategyTypeConfig>({ groupType: 'filter' });
  const [debugResult, setDebugResult] = useState<DebugResult | null>(null);
  const [isDebugging, setIsDebugging] = useState(false);
  const [templateCache, setTemplateCache] = useState<Record<string, any>>({});
  


  // 🔄 加载模板数据
  useEffect(() => {
    const loadTemplateData = async () => {
      if (inventory.length === 0) return;

      const templateIds = [...new Set([
        ...inventory.map(item => item.template_id),
        ...(currentGroup?.cards.map(card => card.template_id) || [])
      ])];

      try {
        const templates = await templateService.getTemplates(templateIds, token);
        setTemplateCache(prev => ({ ...prev, ...templates }));
      } catch (error) {
        console.error('加载模板数据失败:', error);
      }
    };

    loadTemplateData();
  }, [inventory, currentGroup, token]);
  
  // 🎯 初始化
  useEffect(() => {
    if (inventory.length === 0) {
      loadInventory();
    }
  }, []);
  
  useEffect(() => {
    if (!currentGroup) {
      setCurrentGroupState({
        id: 'temp_group',
        name: '未命名策略',
        description: '',
        cards: [],
        execution_mode: executionMode,
        status: 'inactive',
        performance: {
          total_return: 0,
          win_rate: 0,
          max_drawdown: 0,
          sharpe_ratio: 0
        },
        created_at: new Date(),
        updated_at: new Date()
      });
    }
  }, [executionMode]);
  
  // 🎴 处理卡片添加
  const handleAddCard = useCallback(async (cardData: UnifiedCardData) => {
    if (!currentGroup) return;

    const templateId = (cardData as any).id;

    // 检查库存是否充足
    const availableQuantity = getAvailableQuantity(templateId);
    if (availableQuantity <= 0) {
      alert('❌ 库存不足，无法添加此卡片');
      return;
    }

    // 消耗卡片库存
    const success = await consumeCard(templateId, 1);
    if (!success) {
      alert('❌ 添加卡片失败，请重试');
      return;
    }

    const newCard: StrategyCardInstance = {
      id: `card_${Date.now()}`,
      template_id: templateId,
      parameters: {},
      position: { x: 0, y: 0 },
      created_at: new Date(),
      order: currentGroup.cards.length
    };

    setCurrentGroupState(prev => prev ? {
      ...prev,
      cards: [...prev.cards, newCard],
      updated_at: new Date()
    } : null);
    
    // 保持卡包展开（不自动收回）
  }, [currentGroup]);
  
  // 🖱️ 拖拽处理函数（宽松事件类型以兼容 motion.div）
  const handleDragStart = useCallback((e: any, cardData: UnifiedCardData) => {
    setDraggedCard(cardData);
    if (e?.dataTransfer) {
      e.dataTransfer.effectAllowed = 'copy';
      e.dataTransfer.setData('text/plain', (cardData as any).id);
    }
  }, []);
  
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    setIsDragOver(true);
  }, []);
  
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);
  
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (draggedCard) {
      handleAddCard(draggedCard);
      setDraggedCard(null);
    }
  }, [draggedCard, handleAddCard]);
  
  // 🗑️ 处理卡片移除
  const handleRemoveCard = useCallback(async (cardId: string) => {
    if (!currentGroup) return;

    // 找到要移除的卡片
    const cardToRemove = currentGroup.cards.find(card => card.id === cardId);
    if (!cardToRemove) return;

    // 恢复库存
    try {
      await addCards(cardToRemove.template_id, 1, 'strategy_removal');
    } catch (error) {
      console.error('恢复库存失败:', error);
      // 即使恢复库存失败，也继续移除卡片
    }

    setCurrentGroupState(prev => prev ? {
      ...prev,
      cards: prev.cards.filter(card => card.id !== cardId),
      updated_at: new Date()
    } : null);
  }, [currentGroup, addCards]);
  
  // ⚙️ 处理参数编辑
  const handleEditCard = useCallback(async (card: StrategyCardInstance) => {
    try {
      setParamLoading(true)
      setParamError(undefined)
      setEditingCard(card)

      // 优先使用模板缓存，避免重复请求
      let template = templateCache[card.template_id];
      if (!template) {
        // 如果缓存中没有，则从后端获取
        const tpl = await fetchStrategyTemplate(card.template_id)
        template = tpl?.data || tpl;
      }

      const paramsDef: Record<string, StrategyParameter> = template?.parameters || {}
      const paramGroups: Record<string, any> = template?.parameterGroups || {}

      // 初始值 = 现有参数或模板默认
      const values: Record<string, any> = {}
      Object.keys(paramsDef).forEach(k => {
        const def = paramsDef[k]
        values[k] = card.parameters?.[k] ?? (def as any).default ?? ''
      })

      setEditingParamDefs(paramsDef)
      setEditingParamGroups(paramGroups)
      setEditingParameters(values)
    } catch (e) {
      setParamError((e as Error).message)
      setEditingParamDefs({})
      setEditingParamGroups({})
      setEditingParameters(card.parameters || {})
    } finally {
      setParamLoading(false)
    }
  }, [templateCache]);
  
  const handleSaveParameters = useCallback((parameters: Record<string, any>) => {
    if (!editingCard || !currentGroup) return;
    
    setCurrentGroupState(prev => prev ? {
      ...prev,
      cards: prev.cards.map(card => 
        card.id === editingCard.id 
          ? { ...card, parameters }
          : card
      ),
      updated_at: new Date()
    } : null);
    
    setEditingCard(null);
  }, [editingCard, currentGroup]);
  

  // 💾 保存策略组
  const handleSaveGroup = useCallback(() => {
    if (!currentGroup || currentGroup.cards.length === 0) {
      alert('⚠️ 请先添加策略卡片再保存');
      return;
    }
    setIsSavePanelOpen(true);
  }, [currentGroup]);
  


  // 🎯 策略类型配置处理
  const handleTypeConfigConfirm = useCallback((config: StrategyTypeConfig) => {
    setStrategyTypeConfig(config);
    setShowTypeModal(false);
    
    // 如果当前组存在，更新其类型配置
    if (currentGroup) {
      setCurrentGroupState(prev => prev ? {
        ...prev,
        group_type: config.groupType,
        timing_symbols: config.timingSymbols,
        kline_period: config.klinePeriod,
        updated_at: new Date()
      } : null);
    }
  }, [currentGroup]);

  // 🎯 策略调试处理
  const handleDebugStrategy = useCallback(async () => {
    if (!currentGroup || currentGroup.cards.length === 0) {
      alert('请先添加策略卡片');
      return;
    }

    setIsDebugging(true);
    setDebugResult(null);

    try {
      // 构建调试请求数据 - 不发送策略组ID，直接发送卡片列表
      const debugRequest = {
        cards: currentGroup.cards.map(card => ({
          template_id: card.template_id,
          parameters: card.parameters || {},
          name: templateCache[card.template_id]?.name || card.template_id
        })),
        execution_mode: executionMode,
        group_type: strategyTypeConfig.groupType,
        timing_symbols: strategyTypeConfig.timingSymbols,
        kline_period: strategyTypeConfig.klinePeriod
      };

      console.log('开始调试策略:', debugRequest);
      const resp = await debugStrategy(debugRequest)
      const mapped: DebugResult = {
        success: resp?.success ?? true,
        message: resp?.message || '策略执行完成',
        logs: resp?.logs || [],
        data: resp?.data || {
          signals: [],
          statistics: { total: 0, matched: 0, ratio: '0%' },
          filter_condition: strategyTypeConfig.groupType === 'timing' ? '择时策略' : '选股策略',
          execution_time: new Date().toLocaleString(),
          strategy_type: strategyTypeConfig.groupType
        }
      }
      setDebugResult(mapped)
      setIsDebugging(false)

    } catch (error) {
      console.error('调试失败:', error);
      setDebugResult({
        success: false,
        message: '调试执行失败',
        logs: ['调试过程中发生错误'],
        data: null
      });
      setIsDebugging(false);
    }
  }, [currentGroup, executionMode, strategyTypeConfig]);

  const handleConfirmSave = useCallback(async (name: string, description: string) => {
    if (!currentGroup) return;

    try {
      const payload = {
        name,
        description,
        group_type: strategyTypeConfig.groupType,
        timing_symbols: strategyTypeConfig.timingSymbols,
        kline_period: strategyTypeConfig.klinePeriod,
        cards: currentGroup.cards.map(c => ({ parameters: c.parameters, template_id: c.template_id })),
        execution_mode: currentGroup.execution_mode,
        execution_config: { execution_type: strategyTypeConfig.groupType === 'timing' ? 'continuous' : 'onetime' }
      }
      const resp = await createStrategyGroup(payload as any)
      const newGroupId = resp?.data?.id || resp?.id || ''
      setIsSavePanelOpen(false)
      alert(`✅ 策略组保存成功！\n\n🆔 组ID：${newGroupId}\n📛 名称：${name}\n🎴 卡片数：${currentGroup.cards.length}\n⚙️ 执行模式：${currentGroup.execution_mode}\n🎯 类型：${strategyTypeConfig.groupType === 'timing' ? '择时' : '选股'}`)
    } catch (error) {
      alert('❌ 保存失败：' + (error as Error).message)
    }
  }, [currentGroup, strategyTypeConfig]);

  return (
    <UnifiedMobileNav title="策略工坊" showBottomNav={false} showUserInfo={false}>
      <CreationContent>
        {/* 工具栏区域 */}
        <ToolBarArea>
          <ToolBarLeft>
          </ToolBarLeft>
          
          <ToolBarRight>
            <ModeSelector>
              <ModeOption
                $active={executionMode === 'sequential'}
                onClick={() => setExecutionMode('sequential')}
                whileTap={{ scale: 0.95 }}
              >
                顺序执行
              </ModeOption>
              <ModeOption
                $active={executionMode === 'parallel'}
                onClick={() => setExecutionMode('parallel')}
                whileTap={{ scale: 0.95 }}
              >
                并行执行
              </ModeOption>
            </ModeSelector>
            

            
            <ActionButton
              $variant="success"
              onClick={handleDebugStrategy}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={!currentGroup || currentGroup.cards.length === 0 || isDebugging}
            >
              <span>{isDebugging ? '🔄' : '🧪'}</span>
              <span>{isDebugging ? '调试中...' : '调试'}</span>
            </ActionButton>
            
            <ActionButton
              $variant="primary"
              onClick={() => setIsSavePanelOpen(true)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={!currentGroup || currentGroup.cards.length === 0}
            >
              <span>💾</span>
              <span>保存</span>
            </ActionButton>
          </ToolBarRight>
        </ToolBarArea>

        {/* 工作区域 */}
        <WorkArea>
          {/* 策略配置面板 */}
          <ConfigurationArea>
            <ConfigGrid />
            <ConfigContent>
              {/* 策略组配置区 */}
              <GroupHeader>
                <div style={{ flex: 1 }} />
                <GroupActions>
                <StatBadge $color="#3b82f6">
                  {currentGroup?.cards.length || 0} 张卡片
                </StatBadge>
                <StatBadge $color={
                  executionMode === 'sequential' ? '#10b981' : '#f59e0b'
                }>
                  {executionMode === 'sequential' ? '📋 串行' : '🚀 并行'}
                </StatBadge>
              </GroupActions>
            </GroupHeader>
            
            {/* 策略卡片区域 */}
            <CardsArea 
              className={isDragOver ? 'drag-over' : ''}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {currentGroup && currentGroup.cards.length > 0 ? (
                <CardsGrid>
                  <AnimatePresence mode="popLayout">
                    {currentGroup.cards.map((card, index) => {
                      // 使用模板缓存而不是库存数据
                      const template = templateCache[card.template_id];
                      if (!template) return null;

                      const coreSentence = (() => {
                        try {
                          // 使用模板的参数定义和参数组
                          const params = template.parameters || {}
                          const groups = template.parameterGroups || {}
                          const values = card.parameters || {}
                          return buildCoreSentence(params, groups, values)
                        } catch (error) {
                          console.warn(`生成卡牌 ${card.template_id} 核心句式失败:`, error)
                          return template.description || ''
                        }
                      })()

                      const cardData: UnifiedCardData = {
                        id: card.template_id as any,
                        name: template.name || card.template_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                        description: template.description || '策略卡牌',
                        category: template.category || 'timing',
                        rarity: template.rarity || 'common',
                        coreSentence,
                        level: 1
                      } as any;
                      
                      return (
                        <motion.div
                          key={card.id}
                          initial={{ opacity: 0, scale: 0.8, y: 20 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          exit={{ opacity: 0, scale: 0.8, y: -20 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          layout
                        >
                          <ConfigCardWrapper
                            $isSelected={selectedCardId === card.id}
                            onClick={() => handleEditCard(card)}
                          >
                            <CardAnchor>
                              <UniversalCard
                                card={cardData}
                                variant="collection"
                                size="sm"
                                interactive={true}
                                showStats={true}
                                showLevel={true}
                              />
                              <CardRemoveButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveCard(card.id);
                                }}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                −
                              </CardRemoveButton>
                            </CardAnchor>
                          </ConfigCardWrapper>
                        </motion.div>
                      );
                    })}
                  </AnimatePresence>
                </CardsGrid>
              ) : (
                <EmptyCardsHint
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="icon">🎯</div>
                  <div className="title">开始构建您的策略</div>
                  <div className="subtitle">拖动卡片到这个区域，点击卡片配置参数</div>
                </EmptyCardsHint>
              )}
            </CardsArea>
          </ConfigContent>
        </ConfigurationArea>
      </WorkArea>

      {/* 库存区域 */}
      <InventoryArea 
        $isExpanded={isInventoryExpanded}
        initial={false}
        animate={isInventoryExpanded ? 'expanded' : 'collapsed'}
      >
        <InventoryToggle
          onClick={() => setIsInventoryExpanded(!isInventoryExpanded)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>{isInventoryExpanded ? '收起卡包' : '展开卡包'}</span>
        </InventoryToggle>
        
        <InventoryHeader $isExpanded={isInventoryExpanded}>
          <InventoryTitle $isExpanded={isInventoryExpanded}>我的卡牌</InventoryTitle>
        </InventoryHeader>
        
        <InventoryContent $isExpanded={isInventoryExpanded}>
          {(inventory.length > 0 ? inventory.filter(item => {
              const template = templateCache[item.template_id]
              if (!template) return true
              return strategyTypeConfig.groupType === 'filter' ? template.category === 'filter' : template.category === 'timing'
            }) : []).length > 0 ? (
            <InventoryGrid>
              <AnimatePresence mode="popLayout">
                {inventory.filter(item => {
                const template = templateCache[item.template_id]
                if (!template) return true
                return strategyTypeConfig.groupType === 'filter' ? template.category === 'filter' : template.category === 'timing'
              }).map((item, index) => {
                  // 从缓存的模板数据中获取信息
                  const template = templateCache[item.template_id];

                  const cardData: UnifiedCardData = {
                    id: item.template_id as any,
                    name: template?.name || item.template_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    description: template?.description || '策略卡牌',
                    category: template?.category || 'timing',
                    rarity: template?.rarity || 'common',
                    coreSentence: template?.coreSentence || template?.description || '',
                    level: 1
                  } as any;
                  
                  return (
                    <motion.div
                      key={item.template_id}
                      initial={{ opacity: 0, scale: 0.8, y: 20 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.8, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.02 }}
                      layout
                    >
                      <InventoryCardWrapper 
                        $available={item.quantity > 0}
                        draggable={item.quantity > 0}
                        onDragStart={(e) => { if (item.quantity > 0) handleDragStart(e, cardData) }}
                        onClick={() => item.quantity > 0 && handleAddCard(cardData)}
                      >
                        <CardAnchor>
                          <UniversalCard
                            card={cardData}
                            variant="inventory"
                            size="sm"
                            interactive={item.quantity > 0}
                            disabled={item.quantity === 0}
                            showStats={true}
                            showLevel={true}
                          />
                          <QuantityBadge $count={item.quantity}>
                            ×{item.quantity}
                          </QuantityBadge>
                        </CardAnchor>
                      </InventoryCardWrapper>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </InventoryGrid>
          ) : (
            <EmptyCardsHint
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="icon">📦</div>
              <div className="title">库存为空</div>
              <div className="subtitle">前往商店获取策略卡片</div>
            </EmptyCardsHint>
          )}
        </InventoryContent>
      </InventoryArea>

      {/* 参数编辑器（外部组件） */}
      {!!editingCard && (
        <div>
          {paramError && <div style={{color:'#ef4444', padding:'8px 12px'}}>参数加载失败：{paramError}</div>}
          <AdvancedParameterModal
            open={!!editingCard}
            parameters={editingParamDefs}
            parameterGroups={editingParamGroups}
            values={editingParameters}
            onChange={(vals) => setEditingParameters(vals)}
            onClose={() => setEditingCard(null)}
            onConfirm={(vals) => {
              setEditingParameters(vals)
              // 写回当前卡片参数
              setCurrentGroupState(prev => prev ? {
                ...prev,
                cards: prev.cards.map(c => c.id === editingCard?.id ? { ...c, parameters: vals } : c),
                updated_at: new Date()
              } : null)
              setEditingCard(null)
            }}
          />
        </div>
      )}
      
      {/* 策略类型配置模态框 */}
      <StrategyTypeModal
        open={showTypeModal}
        onCancel={() => setShowTypeModal(false)}
        onConfirm={handleTypeConfigConfirm}
      />
      
      {/* 调试控制台 */}
      <DebugConsole
        result={debugResult}
        onClose={() => setDebugResult(null)}
        strategyType={strategyTypeConfig.groupType}
        loading={isDebugging}
      />
      
      <SaveStrategyPanel
        open={isSavePanelOpen}
        initialName={currentGroup?.name ?? '未命名策略'}
        initialDescription={currentGroup?.description ?? ''}
        onCancel={() => setIsSavePanelOpen(false)}
        onConfirm={handleConfirmSave}
      />
      </CreationContent>
    </UnifiedMobileNav>
  );
}

export default StrategyCreationScene;