/**
 * 🎮 游戏相关类型定义 - 统一类型系统
 */

// 🎯 场景类型定义
export type GameScene =
  | 'WorldMap'
  | 'Adventure'
  | 'Arena'
  | 'Battle'
  | 'Codex'
  | 'CardDraw'
  | 'Strategy'
  | 'Monitor'
  | 'StrategyCreation'
  | 'StrategyOptimization'
  | 'Shop'
  | 'Profile'
  | 'Settings'
  | 'Backtest';

export type SceneTransition =
  | 'fade'
  | 'slide-left'
  | 'slide-right'
  | 'slide-up'
  | 'slide-down'
  | 'zoom-in'
  | 'zoom-out'
  | 'flip'
  | 'wipe';

// 🎨 场景数据接口
export interface SceneData {
  [key: string]: any;
}

// 🎭 场景组件属性
export interface SceneComponentProps {
  sceneData?: SceneData;
  onSceneDataChange?: (data: any) => void;
}

// 🎴 策略卡牌核心类型定义
export interface StrategyTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  parameters: Record<string, any>;
  coreSentence?: string; // 卡片展示的核心句式
  icon?: string;
  tags?: string[];
}

export interface InventoryItem {
  template_id: string;
  quantity: number;
  acquired_at: Date;
  total_acquired: number;
  source: 'activity' | 'purchase' | 'reward' | 'achievement';
}

export interface StrategyCardInstance {
  id: string;
  template_id: string;
  parameters: Record<string, any>;
  position: { x: number; y: number };
  created_at: Date;
  order: number;
}

export interface StrategyGroup {
  id: string;
  name: string;
  description: string;
  cards: StrategyCardInstance[];
  execution_mode: 'sequential' | 'parallel';
  status: 'active' | 'inactive' | 'testing' | 'error';
  performance: {
    total_return: number;
    win_rate: number;
    max_drawdown: number;
    sharpe_ratio: number;
  };
  created_at: Date;
  updated_at: Date;
}

export interface GameSession {
  user_id: string;
  session_start: Date;
  current_scene: string;
  total_playtime: number;
  achievements_unlocked: string[];
}

// 🎵 音频类型定义
export type SoundType =
  | 'ui_click'
  | 'ui_hover'
  | 'ui_success'
  | 'ui_error'
  | 'ui_notification'
  | 'card_flip'
  | 'card_play'
  | 'card_draw'
  | 'battle_hit'
  | 'battle_win'
  | 'battle_lose'
  | 'ambient_background'
  | 'ambient_battle'
  | 'ambient_menu';

export interface SoundConfig {
  src: string[];
  volume?: number;
  loop?: boolean;
  preload?: boolean;
  pool?: number;
}

// 🎨 主题类型定义
export type ThemeMode = 'dark' | 'light';

// 🎯 稀有度类型
export type RarityType = 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';

// 🎮 游戏状态类�?export type GameStatus = 'loading' | 'ready' | 'playing' | 'paused' | 'error';

// 📊 性能指标类型
export interface PerformanceMetrics {
  total_return: number;
  win_rate: number;
  max_drawdown: number;
  sharpe_ratio: number;
  volatility?: number;
  alpha?: number;
  beta?: number;
}
