/**
 * 🎯 信号表格组件 - 赛博朋克风格
 * 基于旧版前端SignalTable重构
 */

import React, { useMemo } from 'react';
import type { Stock } from './types';
import './SignalTable.scss';

interface SignalTableProps {
  signals: Stock[];
  className?: string;
}

// 工具函数
const formatNumber = (value: any, unit: string = '', decimalPlaces: number = 2): string => {
  if (value === undefined || value === null) return '-';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  if (isNaN(numValue)) return '-';
  
  return numValue.toFixed(decimalPlaces) + unit;
};

const formatMarketCap = (value: any): string => {
  if (value === undefined || value === null) return '-';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  if (isNaN(numValue)) return '-';
  
  if (numValue >= 100000000) {
    return (numValue / 100000000).toFixed(2) + '亿';
  } else if (numValue >= 10000) {
    return (numValue / 10000).toFixed(2) + '万';
  } 
  return numValue.toFixed(2);
};

const getChangeColor = (value: number): string => {
  return value > 0 ? '#50fa7b' : (value < 0 ? '#ff5555' : '#f8f8f2');
};

const SignalTable: React.FC<SignalTableProps> = ({ signals = [], className = '' }) => {
  // 标准化处理信号数据
  const stockList = useMemo(() => {
    // 开发环境下记录调试信息
    if (process.env.NODE_ENV !== 'production' && signals.length > 0) {
      console.log('信号数据示例:', signals[0]);
    }
    
    return signals.map((signal: Stock) => {
      // 获取股票代码
      const symbol = signal.symbol || signal.code;
      
      // 优先使用后端整合好的market_data
      const marketData = signal.market_data || {};
      
      // 创建标准化的股票对象
      return {
        key: symbol,
        symbol: symbol,
        name: signal.name || marketData.name || symbol,
        price: signal.price || marketData.price || 0,
        change_pct: marketData.day60_change || 0,
        market_cap: marketData.market_cap || 0,
        industry: marketData.industry || '-',
        // 保留原始数据以供需要
        originalData: signal
      };
    });
  }, [signals]);

  if (!stockList || stockList.length === 0) {
    return (
      <div className="signal-table-container">
        <div className="results-header">
          <span className="results-title">筛选结果</span>
          <span className="results-count">0 只股票</span>
        </div>
        <div className="empty-result">
          <span className="empty-text">暂无符合条件的股票</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`signal-table-container ${className}`}>
      <div className="results-header">
        <span className="results-title">筛选结果</span>
        <span className="results-count">{stockList.length} 只股票</span>
      </div>
      
      <div className="signals-table">
        <table className="custom-table">
          <thead>
            <tr>
              <th>代码</th>
              <th>名称</th>
              <th>价格</th>
              <th>涨跌幅</th>
              <th>市值</th>
              <th>行业</th>
            </tr>
          </thead>
          <tbody>
            {stockList.map((stock) => (
              <tr key={stock.key}>
                <td>
                  <span className="stock-code">{stock.symbol}</span>
                </td>
                <td>
                  <span className="stock-name">{stock.name}</span>
                </td>
                <td>
                  <span className="stock-price">¥{formatNumber(stock.price)}</span>
                </td>
                <td>
                  <span 
                    style={{ color: getChangeColor(stock.change_pct) }}
                    className="stock-change"
                  >
                    {stock.change_pct > 0 ? '+' : ''}{formatNumber(stock.change_pct, '%')}
                  </span>
                </td>
                <td>
                  <span className="stock-market-cap">{formatMarketCap(stock.market_cap)}</span>
                </td>
                <td>
                  <span className="stock-industry">{stock.industry}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {stockList.length > 10 && (
          <div className="table-pagination">
            <span>显示 1-{Math.min(10, stockList.length)} 条，共 {stockList.length} 条</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SignalTable;
