/**
 * 📊 策略信号表格组件
 * 统一处理选股和择时信号的展示
 */

import React, { useMemo } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import type { SignalTableProps, FilterSignal, TimingSignal } from './types';

const TableContainer = styled(motion.div)`
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const TableHeader = styled.div`
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: "Inter", sans-serif;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  background: rgba(59, 130, 246, 0.05);
`;

const TableBody = styled.tbody``;

const TableRow = styled(motion.tr)`
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: rgba(59, 130, 246, 0.02);
  }
`;

const TableHeaderCell = styled.th`
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  font-family: "Inter", sans-serif;
`;

const TableCell = styled.td`
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-family: "Inter", sans-serif;
`;

const SymbolCell = styled(TableCell)`
  font-weight: 600;
  color: #374151;
  font-family: "JetBrains Mono", "Consolas", monospace;
`;

const PriceCell = styled(TableCell)<{ $positive?: boolean }>`
  font-weight: 600;
  color: ${props => props.$positive === true ? '#10b981' : props.$positive === false ? '#ef4444' : '#374151'};
`;

const DirectionBadge = styled.span<{ $direction: 'buy' | 'sell' | 'hold' }>`
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  background: ${props => {
    switch(props.$direction) {
      case 'buy': return '#10b981';
      case 'sell': return '#ef4444';
      case 'hold': return '#6b7280';
      default: return '#6b7280';
    }
  }};
`;

const StrengthBar = styled.div<{ $strength: number }>`
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: ${props => props.$strength}%;
    background: linear-gradient(90deg, #10b981, #3b82f6);
    border-radius: 3px;
    transition: width 0.3s ease;
  }
`;

const EmptyState = styled.div`
  padding: 3rem;
  text-align: center;
  color: #6b7280;
`;

const LoadingState = styled.div`
  padding: 3rem;
  text-align: center;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const Spinner = styled(motion.div)`
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
`;

const SignalTable: React.FC<SignalTableProps> = ({ 
  signals, 
  strategyType, 
  loading = false 
}) => {
  const isTimingSignals = (signals: any[]): signals is TimingSignal[] => {
    return strategyType === 'timing' || (signals.length > 0 && 'direction' in signals[0]);
  };

  const tableTitle = useMemo(() => {
    if (loading) return '执行中...';
    if (!signals || signals.length === 0) return '无信号结果';
    
    const signalCount = signals.length;
    const signalType = isTimingSignals(signals) ? '交易信号' : '选股结果';
    return `${signalType} (${signalCount}条)`;
  }, [signals, loading, strategyType]);

  if (loading) {
    return (
      <TableContainer
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <TableHeader>{tableTitle}</TableHeader>
        <LoadingState>
          <Spinner
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          策略执行中...
        </LoadingState>
      </TableContainer>
    );
  }

  if (!signals || signals.length === 0) {
    return (
      <TableContainer
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <TableHeader>{tableTitle}</TableHeader>
        <EmptyState>
          没有找到符合条件的{isTimingSignals([]) ? '交易信号' : '股票'}
        </EmptyState>
      </TableContainer>
    );
  }

  return (
    <TableContainer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <TableHeader>{tableTitle}</TableHeader>
      
      <Table>
        <TableHead>
          <TableRow>
            <TableHeaderCell>代码</TableHeaderCell>
            <TableHeaderCell>名称</TableHeaderCell>
            <TableHeaderCell>价格</TableHeaderCell>
            <TableHeaderCell>涨跌幅</TableHeaderCell>
            {isTimingSignals(signals) ? (
              <>
                <TableHeaderCell>方向</TableHeaderCell>
                <TableHeaderCell>信号强度</TableHeaderCell>
                <TableHeaderCell>时间</TableHeaderCell>
              </>
            ) : (
              <>
                <TableHeaderCell>成交量</TableHeaderCell>
                <TableHeaderCell>市值</TableHeaderCell>
              </>
            )}
            <TableHeaderCell>原因</TableHeaderCell>
          </TableRow>
        </TableHead>
        
        <TableBody>
          {signals.map((signal, index) => (
            <TableRow
              key={`${signal.symbol}-${index}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <SymbolCell>{signal.symbol}</SymbolCell>
              <TableCell>{signal.name}</TableCell>
              <PriceCell>¥{(signal.price || 0).toFixed(2)}</PriceCell>
              <PriceCell $positive={(signal.change_pct || 0) > 0 ? true : (signal.change_pct || 0) < 0 ? false : undefined}>
                {(signal.change_pct || 0) > 0 ? '+' : ''}{(signal.change_pct || 0).toFixed(2)}%
              </PriceCell>
              
              {isTimingSignals(signals) ? (
                <>
                  <TableCell>
                    <DirectionBadge $direction={(signal as TimingSignal).direction}>
                      {(signal as TimingSignal).direction === 'buy' ? '买入' : 
                       (signal as TimingSignal).direction === 'sell' ? '卖出' : '持有'}
                    </DirectionBadge>
                  </TableCell>
                  <TableCell>
                    <StrengthBar $strength={(signal as TimingSignal).signal_strength || 0} />
                    <span style={{ fontSize: '0.75rem', marginTop: '0.25rem', display: 'block' }}>
                      {((signal as TimingSignal).signal_strength || 0).toFixed(1)}%
                    </span>
                  </TableCell>
                  <TableCell>{new Date((signal as TimingSignal).timestamp).toLocaleTimeString()}</TableCell>
                </>
              ) : (
                <>
                  <TableCell>{(((signal as FilterSignal).volume || 0) / 10000).toFixed(0)}万</TableCell>
                  <TableCell>
                    {(signal as FilterSignal).market_cap
                      ? `${((signal as FilterSignal).market_cap! / 100000000).toFixed(0)}亿`
                      : '-'
                    }
                  </TableCell>
                </>
              )}
              
              <TableCell title={signal.reason}>
                {signal.reason ? (signal.reason.length > 20 ? `${signal.reason.slice(0, 20)}...` : signal.reason) : '-'}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default SignalTable; 