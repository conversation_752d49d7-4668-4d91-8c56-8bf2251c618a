"""
策略相关路由
"""
import os
import importlib
import logging
import functools
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body, Request
from fastapi.responses import JSONResponse
from bson import ObjectId
import pandas as pd
import time

from ....core.deps import get_current_user, get_strategy_runtime, get_strategy_service, get_market_data_service, get_stock_info_service
from ....core.runtime.types import RuntimeContext, Signal, StrategyMode, ExecutionMode
from ....core.runtime import StrategyRuntime
from ....utils.response_utils import ResponseFormatter
from ....utils.error_utils import <PERSON>rror<PERSON><PERSON><PERSON>, APIError
from ....models.user import User
from ....models.strategy import StrategyCard, StrategyExecutionHistory
from ....models.strategy_group import StrategyGroup
from ....models.strategy_template import StrategyTemplate
from ....services.strategy_service import StrategyService
from ....services.market_data_service import MarketDataService
from ....services.stock_info_service import StockInfoService
from ....schemas.strategy import (
    StrategyCardCreate,
    StrategyCardUpdate,
    StrategyCardResponse,
    StrategyGroupCreate,
    StrategyGroupUpdate,
    StrategyGroupResponse,
    StrategyExecutionRequest,
    DebugRequest
)

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取基础目录
base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# API错误处理装饰器
def api_error_handler(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except APIError as e:
            ErrorHandler.handle_api_error(e)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"API错误: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=str(e))
    return wrapper

@router.post("/strategies", response_model=Dict[str, Any])
@api_error_handler
async def create_strategy(
    data: Dict[str, Any],
    runtime: StrategyRuntime = Depends(get_strategy_runtime),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """创建策略"""
    strategy = await runtime.strategy_service.create_strategy(data, current_user["id"])
    return ResponseFormatter.success(data=strategy)

@router.post("/debug", response_model=Dict[str, Any])
@api_error_handler
async def debug_strategy(
    request: DebugRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service),
    market_data_service: MarketDataService = Depends(get_market_data_service),
    stock_info_service: StockInfoService = Depends(get_stock_info_service)
):
    """
    执行策略并返回结果
    支持单个策略或策略组执行
    支持调试模式和正常模式
    支持顺序执行和并行执行
    支持过滤模式、择时模式和回测模式
    
    已优化：
    - 自动获取市场数据并整合到信号中，减少前端请求
    - 性能优化：只返回必要字段(价格、市值、60日涨跌幅)，一次获取全部行情数据
    - 架构优化：拆分服务职责，使用专门的StockInfoService处理MongoDB数据
    """
    start_time = time.time()
    
    # 记录更详细的调试信息
    request_dict = request.dict()
    group_type = request_dict.get("group_type", "filter")
    cards_count = len(request_dict.get("cards", []))
    logger.info(f"开始调试策略组: 卡片数量={cards_count}, 模式: {request.execution_mode}, 类型: {group_type}")

    # 把strategy_mode同步到group_type (确保一致性)
    if group_type == "timing" and request_dict.get("strategy_mode") != "timing":
        request_dict["strategy_mode"] = "timing"
        logger.info(f"将策略模式设置为timing以匹配group_type")
    
    # 执行策略获取结果
    result = await strategy_service.execute_strategy(
        request_data=request_dict,
        user_id=current_user["id"],
        debug=True
    )
    
    # 如果执行成功且有信号数据，获取并整合市场数据
    if result and (result.get("success") or result.get("data")):
        signals = result.get("data", {}).get("signals", [])
        
        # 如果有信号数据，并且是选股策略(filter类型)，才获取市场数据
        # 择时策略(timing类型)不需要额外市场数据，因为信号本身已包含交易信息
        if signals and group_type == "filter":
            try:
                # 从结果的信号中提取股票代码
                symbols = []
                for signal in signals:
                    symbol = signal.get("symbol") or signal.get("code") or ""
                    if symbol:
                        symbols.append(str(symbol))
                
                unique_symbols = list(set(symbols))
                
                if unique_symbols:
                    logger.info(f"开始获取 {len(unique_symbols)} 只股票的行情和基础信息")
                    
                    # 并行执行两个异步任务，分别从不同数据源获取数据
                    import asyncio
                    # 获取实时行情数据(SQL数据库)
                    market_data_task = market_data_service.get_market_data(unique_symbols)
                    # 获取基础信息数据(MongoDB数据库)
                    industry_task = stock_info_service.get_industry_info(unique_symbols)
                    
                    # 并行等待所有任务完成
                    quotes, industry_map = await asyncio.gather(market_data_task, industry_task)
                    
                    # 创建代码到行情数据的映射，加速查找
                    quotes_map = {quote["symbol"]: quote for quote in quotes}
                    
                    # 合并行情数据和行业信息到信号中
                    for signal in signals:
                        symbol = signal.get("symbol") or signal.get("code") or ""
                        quote = quotes_map.get(symbol, {})
                        industry = industry_map.get(symbol, "")
                        
                        # 创建精简的市场数据对象，包含行业信息
                        market_data = {
                            "name": quote.get("name", ""),
                            "price": quote.get("price", 0),
                            "market_cap": quote.get("market_cap", 0),
                            "day60_change": quote.get("day60_change", 0),
                            "industry": industry
                        }
                        
                        # 使用精简数据
                        signal["market_data"] = market_data
                    
                    # 记录数据获取完成
                    logger.info(f"成功整合行情和行业数据: {len(quotes)}条记录")
            
            except Exception as e:
                logger.error(f"获取市场数据失败: {str(e)}", exc_info=True)
                # 出错时不影响主流程返回
        elif signals and group_type == "timing":
            # 择时策略不需要额外获取市场数据，信号中已包含交易方向等信息
            logger.info(f"择时策略执行完成，共生成 {len(signals)} 个信号，跳过行情获取步骤")
    
    # 记录总耗时
    elapsed = time.time() - start_time
    logger.info(f"策略调试完成，总耗时: {elapsed:.3f}秒")
    
    return result

@router.post("/timing", response_model=Dict[str, Any])
@api_error_handler
async def execute_timing_strategy(
    request: StrategyExecutionRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """
    执行择时策略并返回结果
    
    专门为择时策略设计的端点，可以处理高频数据信号
    支持设置实时数据参数和数据回溯天数
    自动使用QuestDB获取高频数据进行择时分析
    高性能设计，省略市场数据获取步骤，直接返回买卖信号
    
    Args:
        request: 策略执行请求
        current_user: 当前用户
        strategy_service: 策略服务
        
    Returns:
        Dict[str, Any]: 策略执行结果，包含买卖信号
    """
    start_time = time.time()
    logger.info(f"开始执行择时策略: {request.id}")
    
    # 强制设置execution_mode为"timing"，确保以择时模式执行
    request_data = request.dict()
    request_data["execution_mode"] = "timing"
    
    # 执行策略获取结果
    result = await strategy_service.execute_strategy(
        request_data=request_data,
        user_id=current_user["id"],
        debug=True
    )
    
    # 记录总耗时
    elapsed = time.time() - start_time
    logger.info(f"择时策略执行完成，总耗时: {elapsed:.3f}秒")
    
    return result

@router.get("/", response_model=Dict[str, Any])
@api_error_handler
async def list_strategies(
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    name: Optional[str] = None,
    tag: Optional[str] = None,
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取策略列表"""
    skip = (page - 1) * pageSize
    strategies, total = await strategy_service.list_strategies(
        user_id=current_user["id"],
        skip=skip,
        limit=pageSize,
        name=name,
        tag=tag
    )
    
    # 返回符合前端期望的分页响应格式
    return {
        "success": True,
        "message": "获取策略列表成功",
        "data": {
            "items": strategies,
            "total": total,
            "page": page,
            "pageSize": pageSize
        }
    }

@router.get("/templates", response_model=Dict[str, Any])
@api_error_handler
async def list_strategy_templates(
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    strategy_type: Optional[str] = None,
    tags: Optional[str] = None,
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取策略模板列表
    
    支持按策略功能类型(strategy_type)和标签(tags)筛选
    tags参数可以提供多个标签，用逗号分隔，如"基本面,成长"
    """
    skip = (page - 1) * pageSize
    
    # 解析tags参数
    tag_list = None
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    
    templates, total = await strategy_service.list_strategy_templates(
        skip=skip,
        limit=pageSize,
        strategy_type=strategy_type,
        tags=tag_list
    )
    
    # 直接返回前端期望的分页响应格式，不使用data嵌套
    return {
        "success": True,
        "message": "获取策略模板成功",
        "items": templates,
        "total": total,
        "page": page,
        "pageSize": pageSize
    }

@router.get("/templates/{id}", response_model=Dict[str, Any])
@api_error_handler
async def get_strategy_template(
    id: str,
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取策略模板详情"""
    template = await strategy_service.get_strategy_template(id)
    
    return {
        "success": True,
        "message": "获取策略模板详情成功",
        "data": template
    }

@router.get("/groups", response_model=Dict[str, Any])
@api_error_handler
async def list_strategy_groups(
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取用户的策略组列表"""
    skip = (page - 1) * pageSize
    groups, total = await strategy_service.list_strategy_groups(
        user_id=current_user["id"],
        skip=skip,
        limit=pageSize
    )
    
    # 返回符合前端期望的分页响应格式
    return {
        "success": True,
        "message": "获取策略组列表成功",
        "data": {
            "items": groups,
            "total": total,
            "page": page,
            "pageSize": pageSize
        }
    }

@router.get("/groups/{group_id}", response_model=Dict[str, Any])
@api_error_handler
async def get_strategy_group(
    group_id: str,
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取策略组详情"""
    result = await strategy_service.get_strategy_group(
        group_id=group_id,
        user_id=current_user["id"]
    )
    return ResponseFormatter.success(data=result)

@router.post("/groups", response_model=Dict[str, Any])
@api_error_handler
async def create_strategy_group(
    data: Dict[str, Any] = Body(...),
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """创建策略组"""
    group = await strategy_service.create_strategy_group(
        data=data,
        user_id=current_user["id"]
    )
    return ResponseFormatter.success(data=group)

@router.put("/groups/{group_id}", response_model=Dict[str, Any])
@api_error_handler
async def update_strategy_group(
    group_id: str,
    data: StrategyGroupUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """
    更新策略组
    
    修改策略组的配置信息，但不修改策略组内的策略
    """
    result = await strategy_service.update_strategy_group(
        group_id=group_id,
        user_id=current_user["id"],
        data=data.dict(exclude_unset=True)
    )
    return ResponseFormatter.success(
        data=result,
        message="策略组更新成功"
    )

@router.delete("/groups/{group_id}", response_model=Dict[str, Any])
@api_error_handler
async def delete_strategy_group(
    group_id: str,
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """删除策略组"""
    result = await strategy_service.delete_strategy_group(
        group_id=group_id,
        user_id=current_user["id"]
    )
    return ResponseFormatter.success(data=result)

@router.get("/tags", response_model=Dict[str, Any])
@api_error_handler
async def get_strategy_tags(
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取策略标签列表"""
    tags = await strategy_service.get_strategy_tags()
    return {"success": True, "message": "获取标签成功", "tags": tags}

@router.post("/tags/{tag_name}", response_model=Dict[str, Any])
@api_error_handler
async def update_strategy_tag(
    tag_name: str,
    data: Dict[str, Any] = Body(...),
    strategy_service: StrategyService = Depends(get_strategy_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """更新策略标签"""
    updated_tag = await strategy_service.update_strategy_tag(
        tag_name=tag_name,
        data=data
    )
    return {"success": True, "message": "标签更新成功", "tag": updated_tag}

@router.post("/groups/{group_id}/execute", response_model=Dict[str, Any])
@api_error_handler
async def execute_strategy_group(
    group_id: str,
    mode: str = Query("sequential", description="执行模式：sequential或parallel"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """
    执行策略组
    
    支持串行和并行两种执行模式
    返回执行结果，包含生成的信号
    """
    # 直接返回标准格式的结果
    return await strategy_service.execute_strategy_group(
        group_id=group_id,
        user_id=current_user["id"],
        mode=mode
    )

@router.post("/groups/{group_id}/start", response_model=Dict[str, Any])
@api_error_handler
async def start_strategy_group(
    group_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """
    启动策略组持续执行
    
    对于择时策略，将在交易时段按照K线周期持续执行
    """
    result = await strategy_service.start_strategy_group(
        group_id=group_id,
        user_id=current_user["id"]
    )
    return ResponseFormatter.success(
        data=result,
        message="策略组已启动持续执行"
    )

@router.post("/groups/{group_id}/stop", response_model=Dict[str, Any])
@api_error_handler
async def stop_strategy_group(
    group_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """
    停止策略组持续执行
    
    停止后会清理数据缓存
    """
    result = await strategy_service.stop_strategy_group(
        group_id=group_id,
        user_id=current_user["id"]
    )
    return ResponseFormatter.success(
        data=result,
        message="策略组已停止执行"
    )

@router.get("/groups/{group_id}/execution-history", response_model=Dict[str, Any])
@api_error_handler
async def get_strategy_group_execution_history(
    group_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """
    获取策略组执行历史
    
    返回策略组的执行历史记录，包括执行时间、状态、结果等
    """
    result = await strategy_service.get_strategy_group_execution_history(
        group_id=group_id,
        user_id=current_user["id"]
    )
    return ResponseFormatter.success(
        data=result,
        message="获取策略组执行历史成功"
    )
